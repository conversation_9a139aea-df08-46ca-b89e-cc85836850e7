<template>
  <div class="container" v-loading="loading" element-loading-text="加载中...">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="flex-c">
        <h2 class="page-title">{{ isAddMode ? "新增系统" : "编辑系统" }}</h2>
      </div>
    </div>

    <!-- 表单内容 -->
    <el-form
      ref="myForm"
      :model="myForm"
      :rules="formRules"
      label-width="80px"
      label-position="top"
      inline
      class="edit-form"
    >
      <div class="card">
        <div class="cardTitle">基本信息</div>
        <div class="itemList flex-c">
          <el-form-item label="应用编码" prop="yybm" class="item_grid_3">
            <el-input
              v-model="myForm.yybm"
              size="small"
              disabled
              clearable
              placeholder="请输入应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="IRS应用编码" prop="irsbm" class="item_grid_3">
            <el-input
              v-model="myForm.irsbm"
              size="small"
              clearable
              placeholder="请输入IRS应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="云管平台应用编码"
            prop="dxProjectId"
            class="item_grid_3"
          >
            <el-input
              v-model="myForm.dxProjectId"
              size="small"
              clearable
              placeholder="请输入云管平台应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="风暴中心应用编码"
            prop="fbzxbm"
            class="item_grid_3"
          >
            <el-input
              v-model="myForm.fbzxbm"
              size="small"
              clearable
              placeholder="请输入风暴中心应用编码"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用名称" prop="name" class="item_grid_3">
            <el-input
              v-model="myForm.name"
              size="small"
              clearable
              placeholder="请输入应用名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="应用管理员" class="item_grid_3">
            <el-select
              v-model="myForm.adminId"
              clearable
              filterable
              remote
              size="small"
              style="width: 100%"
              placeholder="搜索选择用户"
              :remote-method="searchUserList"
              :loading="userLoading"
            >
              <el-option
                v-for="item in userOptions"
                :key="item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.userId"
              >
                <span style="float: left">{{
                  item.userName || "未设置用户名"
                }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.nickName || "未设置昵称"
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用类型" prop="yylx" class="item_grid_3">
            <el-select
              v-model="myForm.yylx"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylxOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="上线时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.sxsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="运维截止时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.ywjzsj"
              type="date"
              size="small"
              placeholder="日期选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="主运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.zyywry"
              clearable
              filterable
              remote
              reserve-keyword
              size="small"
              style="width: 100%"
              placeholder="输入以检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{
                  item.userName || "未设置用户名"
                }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.nickName || "未设置昵称"
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="辅运维人员" class="item_grid_3">
            <el-select
              v-model="myForm.fyywry"
              clearable
              filterable
              remote
              reserve-keyword
              multiple
              size="small"
              style="width: 100%"
              placeholder="输入以检索运维人员"
              :remote-method="searchYwryList"
              :loading="ywryLoading"
            >
              <el-option
                v-for="item in ywryOptions"
                :key="item.id || item.userId"
                :label="`${item.userName || item.nickName || '未知用户'}`"
                :value="item.id || item.userId"
              >
                <span style="float: left">{{
                  item.userName || "未设置用户名"
                }}</span>
                <span style="float: right; color: #8492a6; font-size: 13px">{{
                  item.nickName || "未设置昵称"
                }}</span>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统状态" prop="xtStatus" class="item_grid_3">
            <el-select
              v-model="myForm.xtStatus"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in xtztOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="建设依据" class="item_grid_3">
            <el-select
              v-model="myForm.jsyj"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jsyjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="政策文件" class="item_grid_3">
            <el-input
              v-model="myForm.zcwj"
              size="small"
              clearable
              placeholder="建设依据若选择政策文件则显示政策文件输入框，否则领导讲话及批示输入框"
            ></el-input>
          </el-form-item>
          <el-form-item label="建设层级" class="item_grid_3">
            <el-select
              v-model="myForm.jscj"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in jscjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否统建" class="item_grid_3">
            <el-select
              v-model="myForm.sftc"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sftcOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="用户范围" class="item_grid_3">
            <el-select
              v-model="myForm.yhfw"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yhfwOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="发布端" class="item_grid_3">
            <el-select
              v-model="myForm.fbd"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in fbdOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="网络环境" class="item_grid_3">
            <el-select
              v-model="myForm.wlhj"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in wlhjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属系统" class="item_grid_3">
            <el-select
              v-model="myForm.sshj"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in sshjOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="应用领域" class="item_grid_3">
            <el-select
              v-model="myForm.yyly"
              clearable
              multiple
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in yylyOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否协同" class="item_grid_3">
            <el-select
              v-model="myForm.isCollaboration"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="多跨场景" class="item_grid_3">
            <el-select
              v-model="myForm.isCrossScenario"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="体系贯通" class="item_grid_3">
            <el-select
              v-model="myForm.isSystemConnected"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="系统地址" class="item_grid_3">
            <el-input
              v-model="myForm.systemUrl"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="健康监测地址" class="item_grid_3">
            <el-input
              v-model="myForm.url"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="属地" prop="sd" class="item_grid_3">
            <el-select
              v-model="myForm.sd"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="dict in dict.type.sd"
                :key="dict.value"
                :value="dict.value"
                :label="dict.label"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="cardTitle">安全信息</div>
        <div class="itemList flex-c">
          <el-form-item label="等保级别" class="item_grid_3">
            <el-select
              v-model="myForm.dbSecurityLevel"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in dbSecurityLevelOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="是否等保备案" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsSecurityFiling"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="等保备案时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbSecurityFilingDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="等保备案编号" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityFilingNo"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保备案机关" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityFilingOrg"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否等保测评" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsSecurityAssessment"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="等保测评机构" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityAssessmentOrg"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保测评得分" class="item_grid_3">
            <el-input
              v-model="myForm.dbSecurityAssessmentScore"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="等保测评时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbSecurityAssessmentDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="是否密码测评" class="item_grid_3">
            <el-select
              v-model="myForm.dbIsCryptoAssessment"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in boolOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="密码测评级别" class="item_grid_3">
            <el-select
              v-model="myForm.dbCryptoAssessmentLevel"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in dbCryptoAssessmentLevelOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="密码测评时间" class="item_grid_3">
            <el-date-picker
              v-model="myForm.dbCryptoAssessmentDate"
              type="date"
              size="small"
              placeholder="时间选择"
              style="width: 100%"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="密码测评编号" class="item_grid_3">
            <el-input
              v-model="myForm.dbCryptoAssessmentNo"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">云资源</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addCloudResource"
            >新增云资源</el-button
          >
        </div>
        <el-table :data="myForm.yzytList" border>
          <el-table-column prop="ip" label="资源IP" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ip"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="zylx" label="资源类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.zylx"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in cloudResourceTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="ycpmc" label="云产品名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ycpmc"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="sllx" label="实例类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.sllx"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in instanceTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gg" label="规格" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.gg"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ktsj" label="开通时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.ktsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column prop="jzsj" label="截止时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.jzsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeCloudResource(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="cardTitle">单位信息</div>
        <div class="itemList flex-c">
          <el-form-item label="建设单位" class="item_grid_3">
            <treeselect
              v-model="myForm.constructionUnitId"
              :options="enabledDeptOptions"
              :show-count="true"
              placeholder="请选择建设单位"
              style="width: 100%"
              size="small"
            />
          </el-form-item>
          <el-form-item label="归口业务处室" class="item_grid_3">
            <el-input
              v-model="myForm.businessDepartment"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位负责人" class="item_grid_3">
            <el-input
              v-model="myForm.unitLeader"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位类型" class="item_grid_3">
            <el-select
              v-model="myForm.unitType"
              clearable
              size="small"
              style="width: 100%"
              placeholder="下拉选择"
            >
              <el-option
                v-for="(item, i) in unitTypeOptions"
                :key="i"
                :value="item.value"
                :label="item.label"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="电话" class="item_grid_3">
            <el-input
              v-model="myForm.phone"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位地址" class="item_grid_3">
            <el-input
              v-model="myForm.address"
              size="small"
              clearable
              placeholder="输入"
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="cardTitle">厂商信息</div>
        <div class="itemList flex-c">
          <el-form-item label="开发厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperId"
              clearable
              filterable
              remote
              :remote-method="searchDeveloperVendors"
              :loading="developerVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in developerVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorId"
              clearable
              filterable
              remote
              :remote-method="searchOperatorVendors"
              :loading="operatorVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in operatorVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorId"
              clearable
              filterable
              remote
              :remote-method="searchSecurityVendors"
              :loading="securityVendorLoading"
              size="small"
              style="width: 100%"
              placeholder="需先在供应商管理页面中维护信息，再在该位置下拉检索供应商"
            >
              <el-option
                v-for="item in securityVendorOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开发厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.developerVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.operatorVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="安全厂商统一社会信用代码" class="item_grid_3">
            <el-input
              v-model="myForm.securityVendorCode"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>

          <el-form-item label="开发厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csDeveloperUserId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="选择该厂商人员"
            >
              <el-option
                v-for="item in developerContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="运维厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csOperatorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in operatorContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="安全厂商联系人" class="item_grid_3">
            <el-select
              v-model="myForm.csSecurityVendorContactId"
              clearable
              size="small"
              style="width: 100%"
              placeholder="输入"
            >
              <el-option
                v-for="item in securityContactOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="开发厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.developerContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              :disabled="true"
            ></el-input>
          </el-form-item>
          <el-form-item label="运维厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.operatorContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
          <el-form-item label="安全厂商联系电话" class="item_grid_3">
            <el-input
              v-model="myForm.securityContactPhone"
              size="small"
              clearable
              placeholder="自动带出"
              disabled
            ></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联项目</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addRelatedProject"
            >新增关联项目</el-button
          >
        </div>
        <el-table :data="myForm.xmList" border>
          <el-table-column prop="xmbm" label="项目编码" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.xmbm"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="xmmc" label="项目名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.xmmc"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="jsbm" label="建设部门" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.jsbm"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="lxsj" label="立项时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.lxsj"
                type="date"
                size="small"
                placeholder="时间选择"
                style="width: 100%"
              ></el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeRelatedProject(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">关联IP</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addRelatedIp"
            >新增关联IP</el-button
          >
        </div>
        <el-table :data="myForm.ipList" border>
          <el-table-column prop="ipName" label="IP名称" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ipName"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ip" label="IP号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.ip"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="dkh" label="端口号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.dkh"
                size="small"
                placeholder="输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="ipStatus" label="IP状态" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.ipStatus"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in ipStatusOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeRelatedIp(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>

      <div class="card">
        <div class="flex-c">
          <div class="cardTitle">使用组件</div>
          <el-button
            icon="el-icon-plus"
            size="small"
            style="margin: 0 0 6px 20px"
            @click="addComponent"
            >新增使用组件</el-button
          >
        </div>
        <el-table :data="myForm.zjList" border>
          <el-table-column prop="type" label="类型" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.type"
                size="small"
                placeholder="下拉选择"
                style="width: 100%"
              >
                <el-option
                  v-for="(item, i) in componentTypeOptions"
                  :key="i"
                  :value="item.value"
                  :label="item.label"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gylId" label="名称" align="center">
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.gylId"
                clearable
                filterable
                remote
                :remote-method="
                  (query) =>
                    searchComponentsByType(query, scope.row.type, scope.$index)
                "
                :loading="componentLoading"
                size="small"
                style="width: 100%"
                placeholder="输入检索"
                @change="(value) => onComponentChange(value, scope.$index)"
              >
                <el-option
                  v-for="item in getComponentOptionsForRow(scope.$index)"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="gys" label="供应商" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.gys"
                size="small"
                placeholder="自动带出"
                :disabled="true"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="bbh" label="版本号" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.bbh"
                size="small"
                placeholder="自动带出"
                :disabled="true"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column
            prop="slId"
            label="关联主机"
            align="center"
            min-width="120"
          >
            <template slot-scope="scope">
              <el-select
                v-model="scope.row.slId"
                clearable
                filterable
                remote
                :remote-method="
                  (query) => searchHostsByName(query, scope.$index)
                "
                :loading="hostLoading"
                size="small"
                style="width: 100%"
                placeholder="输入检索主机"
                @change="(value) => onHostChange(value, scope.$index)"
              >
                <el-option
                  v-for="item in getHostOptionsForRow(scope.$index)"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                ></el-option>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column prop="zjhxz" label="组件哈希值" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.zjhxz"
                size="small"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="yqtzjgx" label="与其他组件关系" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.yqtzjgx"
                size="small"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="xkxx" label="许可信息" align="center">
            <template slot-scope="scope">
              <el-input
                v-model="scope.row.xkxx"
                size="small"
                placeholder="请输入"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column prop="xkdqsj" label="许可到期时间" align="center">
            <template slot-scope="scope">
              <el-date-picker
                v-model="scope.row.xkdqsj"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              >
              </el-date-picker>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="removeComponent(scope.$index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
          <template slot="empty">
            <div class="empty">
              <img src="@/assets/images/property/empty.png" class="img" />
              <span>暂无数据</span>
            </div>
          </template>
        </el-table>
      </div>
    </el-form>

    <!-- 底部操作栏 -->
    <div class="footer-actions">
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button @click="goBack">返回</el-button>
    </div>
  </div>
</template>

<script>
import {
  listMaintenancePersonnel,
  listContactsBySupplier,
} from "@/api/property/maintenancePersonnel";
import { listSupplier } from "@/api/property/supplierManage";
import {
  getApplication,
  addApplication,
  updateApplication,
  listYzyByNameForList,
} from "@/api/property/applicationManage";
import { listUser } from "@/api/system/user";
import { deptTreeSelect } from "@/api/serve/orderlist";
import { listSupplyChain } from "@/api/property/supplyChain";
import Treeselect from "@riophae/vue-treeselect";
import "@riophae/vue-treeselect/dist/vue-treeselect.css";

export default {
  name: "ApplicationEdit",
  components: {
    Treeselect,
  },
  dicts: ["sd"],
  data() {
    return {
      myForm: {
        yybm: "",
        irsbm: "",
        dxProjectId: "",
        fbzxbm: "",
        name: "",
        adminId: "",
        yylx: "",
        sxsj: "",
        ywjzsj: "",
        zyywry: "",
        fyywry: [],
        xtStatus: "",
        jsyj: [],
        zcwj: "",
        jscj: "",
        sftc: "",
        yhfw: [],
        fbd: [],
        wlhj: [],
        sshj: [],
        yyly: [],
        isCollaboration: "",
        isCrossScenario: "",
        isSystemConnected: "",
        systemUrl: "",
        url: "",
        sd: "",
        // 安全信息字段
        dbSecurityLevel: "", // 等保级别
        dbIsSecurityFiling: "", // 是否等保备案
        dbSecurityFilingDate: "", // 等保备案时间
        dbSecurityFilingNo: "", // 等保备案编号
        dbSecurityFilingOrg: "", // 等保备案机关
        dbIsSecurityAssessment: "", // 是否等保测评
        dbSecurityAssessmentOrg: "", // 等保测评机构
        dbSecurityAssessmentScore: "", // 等保测评得分
        dbSecurityAssessmentDate: "", // 等保测评时间
        dbIsCryptoAssessment: "", // 是否密码测评
        dbCryptoAssessmentLevel: "", // 密码测评级别
        dbCryptoAssessmentDate: "", // 密码测评时间
        dbCryptoAssessmentNo: "", // 密码测评编号
        // 云资源列表
        yzytList: [],
        // 单位信息字段
        constructionUnitId: "", // 建设单位
        businessDepartment: "", // 归口业务处室
        unitLeader: "", // 单位负责人
        unitType: "", // 单位类型
        phone: "", // 电话
        address: "", // 单位地址
        // 厂商信息字段
        csDeveloperId: "", // 开发厂商id
        developerVendorCode: "", // 开发厂商统一社会信用代码
        csDeveloperUserId: "", // 开发厂商联系人id
        developerContactPhone: "", // 开发厂商联系电话
        csOperatorId: "", // 运维厂商id
        operatorVendorCode: "", // 运维厂商统一社会信用代码
        csOperatorContactId: "", // 运维厂商联系人id
        operatorContactPhone: "", // 运维厂商联系电话
        csSecurityVendorId: "", // 安全厂商id
        securityVendorCode: "", // 安全厂商统一社会信用代码
        csSecurityVendorContactId: "", // 安全厂商联系人id
        securityContactPhone: "", // 安全厂商联系电话
        // 关联项目列表
        xmList: [],
        // 关联IP列表
        ipList: [],
        // 使用组件列表
        zjList: [],
      },
      boolOptions: [
        { label: "是", value: 1 },
        { label: "否", value: 0 },
      ],
      yylxOptions: [
        { label: "办公后勤类", value: 1 },
        { label: "业务类", value: 2 },
        { label: "信息宣传类", value: 3 },
        { label: "基础支撑类", value: 4 },
      ],
      xtztOptions: [
        { label: "谋划中", value: 1 },
        { label: "建设中", value: 2 },
        { label: "试运行", value: 3 },
        { label: "运行中", value: 4 },
        { label: "停用", value: 5 },
      ],
      jsyjOptions: [
        { label: "政策文件", value: 1 },
        { label: "领导讲话及批示", value: 2 },
        { label: "部门内部会议/文件", value: 3 },
        { label: "其他", value: 4 },
      ],
      jscjOptions: [
        { label: "国家", value: 1 },
        { label: "省级", value: 2 },
        { label: "市级", value: 3 },
        { label: "县(市、区)", value: 4 },
        { label: "乡镇(街道)", value: 5 },
        { label: "村(社区)", value: 6 },
      ],
      sftcOptions: [
        { label: "否", value: 0 },
        { label: "是", value: 1 },
      ],
      yhfwOptions: [
        { label: "处室内部", value: 1 },
        { label: "本部门内部", value: 2 },
        { label: "本级政府用户", value: 3 },
        { label: "地方各级政府用户", value: 4 },
        { label: "社会公众(面向个人)", value: 5 },
        { label: "社会公众(面向法人)", value: 6 },
      ],
      fbdOptions: [
        { label: "浙里办", value: 1 },
        { label: "浙政钉", value: 2 },
        { label: "数字化改革门户", value: 3 },
        { label: "支付宝", value: 4 },
        { label: "微信", value: 5 },
        { label: "网页", value: 6 },
        { label: "园区PC客户端", value: 7 },
        { label: "APP端", value: 8 },
        { label: "政务服务网", value: 9 },
      ],
      wlhjOptions: [
        { label: "是否互联网系统", value: 0 },
        { label: "政务内网", value: 1 },
        { label: "政务外网", value: 2 },
        { label: "互联网", value: 3 },
        { label: "业务专网", value: 4 },
        { label: "单机", value: 5 },
      ],
      sshjOptions: [
        { label: "党政机关整体智治", value: 1 },
        { label: "数字政府", value: 2 },
        { label: "数字经济", value: 3 },
        { label: "数字社会", value: 4 },
        { label: "数字法治", value: 5 },
        { label: "数字文化", value: 6 },
      ],
      yylyOptions: [
        { label: "信用服务", value: 1 },
        { label: "财税金融", value: 2 },
        { label: "医疗卫生", value: 3 },
        { label: "安全生产", value: 4 },
        { label: "社保就业", value: 5 },
        { label: "市场监管", value: 6 },
        { label: "公共安全", value: 7 },
        { label: "社会救助", value: 8 },
        { label: "城建住房", value: 9 },
        { label: "法律服务", value: 10 },
        { label: "交通运输", value: 11 },
        { label: "生活服务", value: 12 },
        { label: "教育文化", value: 13 },
        { label: "气象服务", value: 14 },
        { label: "科技创新", value: 15 },
        { label: "地理空间", value: 16 },
        { label: "资源能源", value: 17 },
        { label: "机构团体", value: 18 },
        { label: "生态环境", value: 19 },
        { label: "其他", value: 20 },
        { label: "工业农业", value: 21 },
        { label: "商贸流通", value: 22 },
      ],
      // 等保级别选项
      dbSecurityLevelOptions: [
        { label: "一级", value: 1 },
        { label: "二级", value: 2 },
        { label: "三级", value: 3 },
      ],
      // 密码测评级别选项
      dbCryptoAssessmentLevelOptions: [
        { label: "一级", value: 1 },
        { label: "二级", value: 2 },
        { label: "三级", value: 3 },
        { label: "四级", value: 4 },
        { label: "五级", value: 5 },
      ],
      // 云资源类型选项
      cloudResourceTypeOptions: [
        { label: "计算服务", value: "计算服务" },
        { label: "数据库服务", value: "数据库服务" },
        { label: "存储服务", value: "存储服务" },
        { label: "网络服务", value: "网络服务" },
        { label: "基础服务", value: "基础服务" },
      ],
      // 实例类型选项
      instanceTypeOptions: [
        { label: "ECS", value: "ECS" },
        { label: "RDS", value: "RDS" },
        { label: "Redis", value: "Redis" },
        { label: "NAS", value: "NAS" },
        { label: "EIP", value: "EIP" },
        { label: "ECS_SC", value: "ECS_SC" },
        { label: "CSA", value: "CSA" },
        { label: "FM", value: "FM" },
      ],
      // 单位类型选项
      unitTypeOptions: [
        { label: "党政机关", value: "党政机关" },
        { label: "国企", value: "国企" },
      ],
      // IP状态选项
      ipStatusOptions: [
        { label: "运行", value: 1 },
        { label: "关闭", value: 2 },
      ],
      // 组件类型选项
      componentTypeOptions: [
        { label: "中间件", value: 1 },
        { label: "系统", value: 2 },
        { label: "数据库", value: 3 },
        { label: "证书", value: 4 },
      ],
      // 组件相关数据
      componentOptions: [],
      componentLoading: false,
      // 每行组件选项（支持不同类型的组件选项）
      componentOptionsMap: {},
      // 主机相关数据
      hostOptions: [],
      hostLoading: false,
      // 每行主机选项
      hostOptionsMap: {},
      // 供应商相关数据（用于组件模块）
      supplierOptions: [],
      supplierLoading: false,
      // 厂商相关数据
      developerVendorOptions: [],
      developerVendorLoading: false,
      developerContactOptions: [],
      operatorVendorOptions: [],
      operatorVendorLoading: false,
      operatorContactOptions: [],
      securityVendorOptions: [],
      securityVendorLoading: false,
      securityContactOptions: [],
      // 运维人员相关数据
      ywryOptions: [],
      ywryLoading: false,
      // 用户管理员相关数据
      userOptions: [],
      userLoading: false,
      // 部门选项
      deptOptions: [],
      enabledDeptOptions: [],
      // 页面加载状态
      loading: false,
      // 页面模式：true为新增模式，false为编辑模式
      isAddMode: false,
      // 表单验证规则
      formRules: {
        name: [{ required: true, message: "请输入应用名称", trigger: "blur" }],
        // yybm: [{ required: true, message: "请输入应用编码", trigger: "blur" }],
        yylx: [
          { required: true, message: "请选择应用类型", trigger: "change" },
        ],
        xtStatus: [
          { required: true, message: "请选择系统状态", trigger: "change" },
        ],
        sd: [{ required: true, message: "请选择属地", trigger: "change" }],
      },
    };
  },
  watch: {
    // 监听开发厂商选择变化
    "myForm.csDeveloperId"(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.developerVendorOptions.find(
          (item) => item.id === newVal
        );
        if (selectedVendor) {
          this.myForm.developerVendorCode = selectedVendor.code || "";
        }
        // 调用API获取该厂商的联系人列表
        this.loadDeveloperContacts(newVal);
      } else {
        this.myForm.developerVendorCode = "";
        this.developerContactOptions = [];
        this.myForm.csDeveloperUserId = "";
        this.myForm.developerContactPhone = "";
      }
    },
    // 监听开发厂商联系人选择变化
    "myForm.csDeveloperUserId"(newVal) {
      if (newVal) {
        const contact = this.developerContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.developerContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.developerContactPhone = "";
      }
    },
    // 监听运维厂商选择变化
    "myForm.csOperatorId"(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.operatorVendorOptions.find(
          (item) => item.id === newVal
        );
        if (selectedVendor) {
          this.myForm.operatorVendorCode = selectedVendor.code || "";
        }
        // 调用API获取该厂商的联系人列表
        this.loadOperatorContacts(newVal);
      } else {
        this.myForm.operatorVendorCode = "";
        this.operatorContactOptions = [];
        this.myForm.csOperatorContactId = "";
        this.myForm.operatorContactPhone = "";
      }
    },
    // 监听运维厂商联系人选择变化
    "myForm.csOperatorContactId"(newVal) {
      if (newVal) {
        const contact = this.operatorContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.operatorContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.operatorContactPhone = "";
      }
    },
    // 监听安全厂商选择变化
    "myForm.csSecurityVendorId"(newVal) {
      if (newVal) {
        // 从选项中找到对应的厂商信息
        const selectedVendor = this.securityVendorOptions.find(
          (item) => item.id === newVal
        );
        if (selectedVendor) {
          this.myForm.securityVendorCode = selectedVendor.code || "";
        }
        // 调用API获取该厂商的联系人列表
        this.loadSecurityContacts(newVal);
      } else {
        this.myForm.securityVendorCode = "";
        this.securityContactOptions = [];
        this.myForm.csSecurityVendorContactId = "";
        this.myForm.securityContactPhone = "";
      }
    },
    // 监听安全厂商联系人选择变化
    "myForm.csSecurityVendorContactId"(newVal) {
      if (newVal) {
        const contact = this.securityContactOptions.find(
          (item) => item.id === newVal
        );
        this.myForm.securityContactPhone = contact ? contact.phone : "";
      } else {
        this.myForm.securityContactPhone = "";
      }
    },
    // 监听组件列表变化，自动填充供应商和版本号
    "myForm.zjList": {
      handler(newVal) {
        newVal.forEach((item, index) => {
          if (item.gylId && !item.gys && !item.bbh) {
            // 从组件选项中找到对应的组件信息
            const selectedComponent = this.componentOptions.find(
              (comp) => comp.id === item.gylId
            );
            if (selectedComponent) {
              this.$set(this.myForm.zjList, index, {
                ...item,
                gys: selectedComponent.supplier,
                bbh: selectedComponent.version,
              });
            }
          }
        });
      },
      deep: true,
    },
  },
  created() {
    this.initData();
    this.getYwryList(); // 初始化运维人员列表
    this.getUserList(); // 初始化用户列表
    this.getDeptTree(); // 初始化部门树
  },
  methods: {
    // 初始化数据
    initData() {
      // 判断是新增还是编辑模式
      const mode = this.$route.query.mode;
      const id = this.$route.query.id;

      if (mode === "add") {
        // 新增模式
        this.isAddMode = true;
        console.log("新增模式：初始化空表单");
        this.initFormForAdd();
      } else if (id) {
        // 编辑模式
        this.isAddMode = false;
        console.log("编辑模式：加载应用数据，ID:", id);
        this.loadApplicationData(id);
      } else {
        // 默认为新增模式
        this.isAddMode = true;
        console.log("默认新增模式：初始化空表单");
        this.initFormForAdd();
      }
    },

    // 初始化新增表单
    initFormForAdd() {
      // 重置表单为默认值
      this.myForm = {
        yybm: "",
        irsbm: "",
        dxProjectId: "",
        fbzxbm: "",
        name: "",
        adminId: "",
        yylx: "",
        sxsj: "",
        ywjzsj: "",
        zyywry: "",
        fyywry: [],
        xtStatus: 1, // 默认状态为建设中
        jsyj: [],
        zcwj: "",
        jscj: "",
        sftc: 0,
        yhfw: [],
        fbd: [],
        wlhj: [],
        sshj: [],
        yyly: [],
        isCollaboration: 0,
        isCrossScenario: 0,
        isSystemConnected: 0,
        systemUrl: "",
        sd: "",
        url: "",
        // 安全信息字段
        dbSecurityLevel: "",
        dbIsSecurityFiling: 0,
        dbSecurityFilingDate: "",
        dbSecurityFilingNo: "",
        dbSecurityFilingOrg: "",
        dbIsSecurityAssessment: 0,
        dbSecurityAssessmentOrg: "",
        dbSecurityAssessmentScore: "",
        dbSecurityAssessmentDate: "",
        dbIsCryptoAssessment: 0,
        dbCryptoAssessmentLevel: "",
        dbCryptoAssessmentDate: "",
        dbCryptoAssessmentNo: "",
        // 单位信息
        constructionUnitId: "",
        businessDepartment: "",
        unitLeader: "",
        unitType: "",
        phone: "",
        address: "",
        // 厂商信息
        csDeveloperId: "",
        csDeveloperUserId: "",
        csOperatorId: "",
        csOperatorContactId: "",
        csSecurityVendorId: "",
        csSecurityVendorContactId: "",
        // 关联数据列表
        xmList: [],
        ipList: [],
        zjList: [],
        yzytList: [],
        // 其他字段
        remark: "",
      };

      console.log("新增表单初始化完成:", this.myForm);
    },

    // 加载应用数据
    async loadApplicationData(id) {
      try {
        this.loading = true;
        console.log("Loading application data for ID:", id);

        const response = await getApplication(id);
        if (response.code === 200 && response.data) {
          const data = response.data;

          // 映射API返回的数据到表单
          this.myForm = {
            ...this.myForm,
            // 基本信息
            id: data.id,
            yybm: data.yybm || "",
            irsbm: data.irsbm || "",
            dxProjectId: data.dxProjectId || "",
            fbzxbm: data.fbzxbm || "",
            name: data.name || "",
            adminId: data.adminId || "",
            yylx: data.yylx || "",
            sxsj: data.sxsj || "",
            ywjzsj: data.ywjzsj || "",
            xtStatus: data.xtStatus || 1,
            jsyj: this.parseMultiSelectValue(data.jsyj),
            zcwj: data.zcwj || "",
            jscj: data.jscj || "",
            sftc: data.sftc !== null && data.sftc !== undefined ? data.sftc : 0,
            yhfw: this.parseMultiSelectValue(data.yhfw),
            fbd: this.parseMultiSelectValue(data.fbd),
            wlhj: this.parseMultiSelectValue(data.wlhj),
            sshj: this.parseMultiSelectValue(data.sshj),
            yyly: this.parseMultiSelectValue(data.yyly),
            isCollaboration:
              data.isCollaboration !== null &&
              data.isCollaboration !== undefined
                ? data.isCollaboration
                : 0,
            isCrossScenario:
              data.isCrossScenario !== null &&
              data.isCrossScenario !== undefined
                ? data.isCrossScenario
                : 0,
            isSystemConnected:
              data.isSystemConnected !== null &&
              data.isSystemConnected !== undefined
                ? data.isSystemConnected
                : 0,
            systemUrl: data.systemUrl || "",
            sd: data.sd || "",
            url: data.url || "",

            // 等保信息
            dbSecurityLevel: data.dbSecurityLevel || "",
            dbIsSecurityFiling:
              data.dbIsSecurityFiling !== null &&
              data.dbIsSecurityFiling !== undefined
                ? data.dbIsSecurityFiling
                : 0,
            dbSecurityFilingDate: data.dbSecurityFilingDate || "",
            dbSecurityFilingNo: data.dbSecurityFilingNo || "",
            dbSecurityFilingOrg: data.dbSecurityFilingOrg || "",
            dbIsSecurityAssessment:
              data.dbIsSecurityAssessment !== null &&
              data.dbIsSecurityAssessment !== undefined
                ? data.dbIsSecurityAssessment
                : 0,
            dbSecurityAssessmentOrg: data.dbSecurityAssessmentOrg || "",
            dbSecurityAssessmentScore: data.dbSecurityAssessmentScore || "",
            dbSecurityAssessmentDate: data.dbSecurityAssessmentDate || "",
            dbIsCryptoAssessment:
              data.dbIsCryptoAssessment !== null &&
              data.dbIsCryptoAssessment !== undefined
                ? data.dbIsCryptoAssessment
                : 0,
            dbCryptoAssessmentLevel: data.dbCryptoAssessmentLevel || "",
            dbCryptoAssessmentDate: data.dbCryptoAssessmentDate || "",
            dbCryptoAssessmentNo: data.dbCryptoAssessmentNo || "",

            // 单位信息
            constructionUnitId: data.constructionUnitId || "",
            businessDepartment: data.businessDepartment || "",
            unitLeader: data.unitLeader || "",
            unitType: data.unitType || "",
            phone: data.phone || "",
            address: data.address || "",

            // 厂商信息
            csDeveloperId: data.csDeveloperId || "",
            csDeveloperUserId: data.csDeveloperUserId || "",
            csOperatorId: data.csOperatorId || "",
            csOperatorContactId: data.csOperatorContactId || "",
            csSecurityVendorId: data.csSecurityVendorId || "",
            csSecurityVendorContactId: data.csSecurityVendorContactId || "",

            // 关联数据列表
            xmList: data.xmList || [],
            ipList: data.ipList || [],
            zjList: data.zjList || [],
            yzytList: data.yzytList || [],

            // 运维人员处理 - 从数组中提取第一个人员的ID
            zyywry:
              data.zywList && data.zywList.length > 0
                ? data.zywList[0].ywryId
                : "",
            // 辅运维人员处理 - 从数组中提取所有人员的ID
            fyywry:
              data.fywList && data.fywList.length > 0
                ? data.fywList.map(item => item.ywryId)
                : [],

            // 其他字段
            remark: data.remark || "",
          };

          // 预加载厂商选项以确保正确回显
          console.log("准备预加载厂商选项...");
          await this.preloadVendorOptions();
          console.log("厂商选项预加载完成");

          // 加载厂商联系人信息
          if (this.myForm.csDeveloperId) {
            this.loadDeveloperContacts(this.myForm.csDeveloperId);
          }
          if (this.myForm.csOperatorId) {
            this.loadOperatorContacts(this.myForm.csOperatorId);
          }
          if (this.myForm.csSecurityVendorId) {
            this.loadSecurityContacts(this.myForm.csSecurityVendorId);
          }

          // 预加载组件选项以确保正确回显
          await this.preloadComponentOptions();

          console.log("Loaded application data:", this.myForm);
        } else {
          this.$message.error(response.msg || "获取应用详情失败");
        }
      } catch (error) {
        console.error("Error loading application data:", error);
        this.$message.error("获取应用详情失败");
      } finally {
        this.loading = false;
      }
    },

    // 保存数据
    async handleSave() {
      this.$refs.myForm.validate(async (valid) => {
        if (valid) {
          try {
            this.loading = true;
            console.log("Saving form data:", this.myForm);
            console.log("Is edit mode:", !!this.myForm.id);

            // 准备保存的数据，确保字段名与API一致
            const saveData = {
              ...this.myForm,
              // 运维人员字段映射 - 将单个ID转换为数组格式
              zywList: this.myForm.zyywry
                ? [{ ywryId: this.myForm.zyywry }]
                : [],
              // 辅运维人员字段映射 - 将多个ID转换为数组格式
              fywList: this.myForm.fyywry && this.myForm.fyywry.length > 0
                ? this.myForm.fyywry.map(id => ({ ywryId: id }))
                : [],

              // 确保数值类型字段为数字
              adminId: this.myForm.adminId
                ? parseInt(this.myForm.adminId)
                : null,
              yylx: this.myForm.yylx ? parseInt(this.myForm.yylx) : null,
              xtStatus: this.myForm.xtStatus
                ? parseInt(this.myForm.xtStatus)
                : 1,
              jsyj: this.formatMultiSelectValue(this.myForm.jsyj),
              jscj: this.myForm.jscj ? parseInt(this.myForm.jscj) : null,
              sftc:
                this.myForm.sftc !== "" &&
                this.myForm.sftc !== null &&
                this.myForm.sftc !== undefined
                  ? parseInt(this.myForm.sftc)
                  : 0,
              yhfw: this.formatMultiSelectValue(this.myForm.yhfw),
              fbd: this.formatMultiSelectValue(this.myForm.fbd),
              wlhj: this.formatMultiSelectValue(this.myForm.wlhj),
              sshj: this.formatMultiSelectValue(this.myForm.sshj),
              yyly: this.formatMultiSelectValue(this.myForm.yyly),
              isCollaboration:
                this.myForm.isCollaboration !== "" &&
                this.myForm.isCollaboration !== null &&
                this.myForm.isCollaboration !== undefined
                  ? parseInt(this.myForm.isCollaboration)
                  : 0,
              isCrossScenario:
                this.myForm.isCrossScenario !== "" &&
                this.myForm.isCrossScenario !== null &&
                this.myForm.isCrossScenario !== undefined
                  ? parseInt(this.myForm.isCrossScenario)
                  : 0,
              isSystemConnected:
                this.myForm.isSystemConnected !== "" &&
                this.myForm.isSystemConnected !== null &&
                this.myForm.isSystemConnected !== undefined
                  ? parseInt(this.myForm.isSystemConnected)
                  : 0,

              // 等保相关数值字段
              dbSecurityLevel: this.myForm.dbSecurityLevel
                ? parseInt(this.myForm.dbSecurityLevel)
                : null,
              dbIsSecurityFiling:
                this.myForm.dbIsSecurityFiling !== "" &&
                this.myForm.dbIsSecurityFiling !== null &&
                this.myForm.dbIsSecurityFiling !== undefined
                  ? parseInt(this.myForm.dbIsSecurityFiling)
                  : 0,
              dbIsSecurityAssessment:
                this.myForm.dbIsSecurityAssessment !== "" &&
                this.myForm.dbIsSecurityAssessment !== null &&
                this.myForm.dbIsSecurityAssessment !== undefined
                  ? parseInt(this.myForm.dbIsSecurityAssessment)
                  : 0,
              dbSecurityAssessmentScore: this.myForm.dbSecurityAssessmentScore
                ? parseFloat(this.myForm.dbSecurityAssessmentScore)
                : null,
              dbIsCryptoAssessment:
                this.myForm.dbIsCryptoAssessment !== "" &&
                this.myForm.dbIsCryptoAssessment !== null &&
                this.myForm.dbIsCryptoAssessment !== undefined
                  ? parseInt(this.myForm.dbIsCryptoAssessment)
                  : 0,
              dbCryptoAssessmentLevel: this.myForm.dbCryptoAssessmentLevel
                ? parseInt(this.myForm.dbCryptoAssessmentLevel)
                : null,

              // 日期字段格式化
              sxsj: this.myForm.sxsj ? this.formatDate(this.myForm.sxsj) : null,
              ywjzsj: this.myForm.ywjzsj
                ? this.formatDate(this.myForm.ywjzsj)
                : null,
              dbSecurityFilingDate: this.myForm.dbSecurityFilingDate
                ? this.formatDate(this.myForm.dbSecurityFilingDate)
                : null,
              dbSecurityAssessmentDate: this.myForm.dbSecurityAssessmentDate
                ? this.formatDate(this.myForm.dbSecurityAssessmentDate)
                : null,
              dbCryptoAssessmentDate: this.myForm.dbCryptoAssessmentDate
                ? this.formatDate(this.myForm.dbCryptoAssessmentDate)
                : null,

              // 移除表单中不需要的字段
              zyywry: undefined,
              developerVendorCode: undefined,
              operatorVendorCode: undefined,
              securityVendorCode: undefined,
              developerContactPhone: undefined,
              operatorContactPhone: undefined,
              securityContactPhone: undefined,
            };

            // 如果是新增操作（没有id），则移除id字段
            if (!this.myForm.id) {
              delete saveData.id;
            }

            console.log("Prepared save data:", saveData);
            console.log("Is add mode:", this.isAddMode);

            let response;
            if (this.isAddMode) {
              // 新增模式：调用新增接口
              console.log("Calling addApplication API");
              response = await addApplication(saveData);
            } else {
              // 编辑模式：调用编辑接口
              console.log("Calling updateApplication API");
              response = await updateApplication(saveData);
            }

            console.log("API response:", response);

            if (response.code === 200) {
              this.$message.success(this.isAddMode ? "新增成功" : "保存成功");
              this.goBack();
            } else {
              console.error("API error:", response);
              this.$message.error(
                response.msg || (this.isAddMode ? "新增失败" : "保存失败")
              );
            }
          } catch (error) {
            console.error("Error saving application data:", error);
            this.$message.error("保存失败");
          } finally {
            this.loading = false;
          }
        } else {
          this.$message.error("请检查表单数据");
        }
      });
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },

    // 新增云资源
    addCloudResource() {
      this.myForm.yzytList.push({
        ip: "",
        zylx: "",
        ycpmc: "",
        sllx: "",
        gg: "",
        ktsj: "",
        jzsj: "",
      });
    },

    // 删除云资源
    removeCloudResource(index) {
      this.myForm.yzytList.splice(index, 1);
    },

    // 新增关联项目
    addRelatedProject() {
      this.myForm.xmList.push({
        xmbm: "",
        xmmc: "",
        jsbm: "",
        lxsj: "",
      });
    },

    // 删除关联项目
    removeRelatedProject(index) {
      this.myForm.xmList.splice(index, 1);
    },

    // 新增关联IP
    addRelatedIp() {
      this.myForm.ipList.push({
        ipName: "",
        ip: "",
        dkh: "",
        ipStatus: 1, // 默认为运行状态
      });
    },

    // 删除关联IP
    removeRelatedIp(index) {
      this.myForm.ipList.splice(index, 1);
    },

    // 新增使用组件
    addComponent() {
      this.myForm.zjList.push({
        type: 1, // 默认为中间件
        gylId: "",
        gys: "",
        bbh: "",
        ip: "",
        slId: "", // 关联主机ID
        zjhxz: "",
        yqtzjgx: "",
        xkxx: "",
        xkdqsj: "",
      });
    },

    // 删除使用组件
    removeComponent(index) {
      this.myForm.zjList.splice(index, 1);
    },

    // 根据类型搜索组件
    async searchComponentsByType(query, type, rowIndex) {
      if (query !== "" && type) {
        this.componentLoading = true;
        try {
          console.log(
            `搜索组件 - 查询词: ${query}, 类型: ${type}, 行索引: ${rowIndex}`
          );

          const response = await listSupplyChain({
            pageNum: 1,
            pageSize: 50,
            name: query, // 按名称模糊查询
            type: type, // 按类型过滤
          });

          if (response.code === 200 && response.data && response.data.list) {
            const components = response.data.list.map((item) => ({
              id: item.id,
              name: item.name,
              supplier: item.gys,
              version: item.bbh,
              ip: item.ip,
              // 拼接显示标签：名称-供应商-版本号
              label: `${item.name || ""}-${item.gys || ""}-${item.bbh || ""}`,
            }));

            // 为特定行设置组件选项
            this.$set(this.componentOptionsMap, rowIndex, components);
            console.log(`行 ${rowIndex} 的组件选项:`, components);
          } else {
            this.$set(this.componentOptionsMap, rowIndex, []);
            console.warn("获取组件列表失败:", response.msg);
          }
        } catch (error) {
          console.error("搜索组件失败:", error);
          this.$set(this.componentOptionsMap, rowIndex, []);
          this.$message.error("搜索组件失败");
        } finally {
          this.componentLoading = false;
        }
      } else {
        this.$set(this.componentOptionsMap, rowIndex, []);
      }
    },

    // 获取指定行的组件选项
    getComponentOptionsForRow(rowIndex) {
      return this.componentOptionsMap[rowIndex] || [];
    },

    // 根据名称搜索主机
    async searchHostsByName(query, rowIndex) {
      if (query && query.trim()) {
        try {
          this.hostLoading = true;
          console.log(`搜索主机 - 查询: ${query}, 行索引: ${rowIndex}`);

          // 调用listYzyAll接口搜索主机
          const response = await listYzyByNameForList({
            name: query.trim(),
          });

          if (response.code === 200 && response.data) {
            const hosts = response.data.map((item) => ({
              // id格式：sjId-uuid-name-sjType
              id: `${item.sjId}-${item.uuid}-${item.name}-${item.sjType}`,
              // name格式：sjType-uuid-name
              name: `${item.sjType}-${item.uuid}-${item.name}`,
              label: `${item.sjType}-${item.uuid}-${item.name}`,
              sjId: item.sjId,
              uuid: item.uuid,
              sjType: item.sjType,
              originalName: item.name,
            }));

            // 为特定行设置主机选项
            this.$set(this.hostOptionsMap, rowIndex, hosts);
            console.log(`行 ${rowIndex} 的主机选项:`, hosts);
          } else {
            this.$set(this.hostOptionsMap, rowIndex, []);
            console.warn("获取主机列表失败:", response.msg);
          }
        } catch (error) {
          console.error("搜索主机失败:", error);
          this.$set(this.hostOptionsMap, rowIndex, []);
          this.$message.error("搜索主机失败");
        } finally {
          this.hostLoading = false;
        }
      } else {
        this.$set(this.hostOptionsMap, rowIndex, []);
      }
    },

    // 获取指定行的主机选项
    getHostOptionsForRow(rowIndex) {
      return this.hostOptionsMap[rowIndex] || [];
    },

    // 主机选择变化处理
    onHostChange(hostId, rowIndex) {
      if (hostId) {
        const hostOptions = this.getHostOptionsForRow(rowIndex);
        const selectedHost = hostOptions.find((item) => item.id === hostId);

        if (selectedHost) {
          console.log(`行 ${rowIndex} 选择主机:`, selectedHost);
          // 这里可以根据需要自动填充其他相关字段
        }
      }
    },

    // 组件选择变化处理
    onComponentChange(componentId, rowIndex) {
      if (componentId) {
        const componentOptions = this.getComponentOptionsForRow(rowIndex);
        const selectedComponent = componentOptions.find(
          (item) => item.id === componentId
        );

        if (selectedComponent) {
          // 自动填充版本号和供应商
          this.$set(
            this.myForm.zjList[rowIndex],
            "bbh",
            selectedComponent.version || ""
          );
          this.$set(
            this.myForm.zjList[rowIndex],
            "gys",
            selectedComponent.supplier || ""
          );
          this.$set(
            this.myForm.zjList[rowIndex],
            "ip",
            selectedComponent.ip || ""
          );

          console.log(`行 ${rowIndex} 选择组件:`, selectedComponent);
          console.log(
            `自动填充 - 版本号: ${selectedComponent.version}, 供应商: ${selectedComponent.supplier}`
          );
        }
      } else {
        // 清空相关字段
        this.$set(this.myForm.zjList[rowIndex], "bbh", "");
        this.$set(this.myForm.zjList[rowIndex], "gys", "");
        this.$set(this.myForm.zjList[rowIndex], "ip", "");
        this.$set(this.myForm.zjList[rowIndex], "slId", "");
      }
    },

    // 预加载组件选项以确保正确回显
    async preloadComponentOptions() {
      try {
        console.log("开始预加载组件选项...");
        console.log("当前组件列表:", this.myForm.zjList);
        try {
          // 调用供应链管理API获取该类型的所有组件
          const response = await listSupplyChain({
            pageNum: 1,
            pageSize: 1000,
          });

          if (response.code === 200 && response.data && response.data.list) {
            const components = response.data.list.map((item) => ({
              id: item.id,
              name: item.name,
              supplier: item.gys,
              version: item.bbh,
              ip: item.ip,
              // 拼接显示标签：名称-供应商-版本号
              label: `${item.name || ""}-${item.gys || ""}-${item.bbh || ""}`,
            }));
            // 遍历每个组件行
            for (let index = 0; index < this.myForm.zjList.length; index++) {
              const component = this.myForm.zjList[index];

              if (component.gylId && component.type) {
                console.log(
                  `预加载第 ${index} 行组件选项 - ID: ${component.gylId}, 类型: ${component.type}`
                );
                // 为该行设置组件选项
                this.$set(this.componentOptionsMap, index, components);

                // 查找当前选中的组件
                const selectedComponent = components.find(
                  (comp) => comp.id == component.gylId
                );
                if (selectedComponent) {
                  console.log(`第 ${index} 行找到匹配组件:`, selectedComponent);
                } else {
                  console.warn(
                    `第 ${index} 行未找到匹配组件 ID: ${component.gylId}`
                  );
                }
              }

              // 预加载主机选项（如果有slId）
              if (component.slId) {
                console.log(
                  `预加载第 ${index} 行主机选项 - slId: ${component.slId}`
                );
                // 从slId中提取原始名称进行搜索
                const parts = component.slId.split("-");
                if (parts.length >= 3) {
                  const originalName = parts[2]; // 获取name部分
                  await this.searchHostsByName(originalName, index);
                }
              }
            }
          }
        } catch (error) {
          console.error(`预加载组件选项失败:`, error);
        }

        console.log("组件选项预加载完成");
        console.log("组件选项映射:", this.componentOptionsMap);

        // 强制更新视图
        this.$forceUpdate();
      } catch (error) {
        console.error("预加载组件选项失败:", error);
      }
    },

    // 搜索供应商（用于组件模块）
    async searchSupplierForComponent(query) {
      if (query !== "") {
        this.supplierLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.supplierOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.supplierOptions = [];
          }
        } catch (error) {
          console.error("搜索供应商失败:", error);
          this.supplierOptions = [];
        } finally {
          this.supplierLoading = false;
        }
      } else {
        this.supplierOptions = [];
      }
    },

    // 搜索开发厂商
    async searchDeveloperVendors(query) {
      if (query !== "") {
        this.developerVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.developerVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.developerVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索开发厂商失败:", error);
          this.developerVendorOptions = [];
        } finally {
          this.developerVendorLoading = false;
        }
      } else {
        this.developerVendorOptions = [];
      }
    },

    // 搜索运维厂商
    async searchOperatorVendors(query) {
      if (query !== "") {
        this.operatorVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.operatorVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.operatorVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索运维厂商失败:", error);
          this.operatorVendorOptions = [];
        } finally {
          this.operatorVendorLoading = false;
        }
      } else {
        this.operatorVendorOptions = [];
      }
    },

    // 搜索安全厂商
    async searchSecurityVendors(query) {
      if (query !== "") {
        this.securityVendorLoading = true;
        try {
          const response = await listSupplier({
            pageNum: 1,
            pageSize: 50,
            gysName: query, // 按供应商名称模糊查询
          });
          if (response.code === 200 && response.data && response.data.list) {
            this.securityVendorOptions = response.data.list.map((item) => ({
              id: String(item.id), // 确保ID为字符串类型
              name: item.gysName,
              code: item.tyxydm, // 统一社会信用代码
            }));
          } else {
            this.securityVendorOptions = [];
          }
        } catch (error) {
          console.error("搜索安全厂商失败:", error);
          this.securityVendorOptions = [];
        } finally {
          this.securityVendorLoading = false;
        }
      } else {
        this.securityVendorOptions = [];
      }
    },

    // 搜索运维人员
    searchYwryList(query) {
      this.ywryLoading = true;
      this.getYwryList(query);
    },

    // 获取运维人员列表
    async getYwryList(keyword = "") {
      try {
        const params = {
          pageNum: 1,
          pageSize: 100, // 增加页面大小以获取更多数据
          status: 1, // 只查询正常状态的人员
        };

        // 如果有关键词，同时按userName和nickName搜索
        if (keyword && keyword.trim()) {
          const trimmedKeyword = keyword.trim();
          // 先尝试按userName搜索
          params.userName = trimmedKeyword;
        }

        console.log("搜索运维人员参数:", params);
        const response = await listMaintenancePersonnel(params);
        console.log("运维人员接口响应:", response);

        if (response.code === 200 && response.data && response.data.list) {
          let list = response.data.list;

          // 如果按userName搜索没有结果，再尝试按nickName搜索
          if (keyword && keyword.trim() && list.length === 0) {
            const nickNameParams = {
              pageNum: 1,
              pageSize: 100,
              status: 1,
              nickName: keyword.trim(),
            };
            console.log("按nickName搜索参数:", nickNameParams);
            const nickNameResponse = await listMaintenancePersonnel(
              nickNameParams
            );
            console.log("按nickName搜索响应:", nickNameResponse);
            if (
              nickNameResponse.code === 0 &&
              nickNameResponse.data &&
              nickNameResponse.data.list
            ) {
              list = nickNameResponse.data.list;
            }
          }

          this.ywryOptions = list;
          console.log("最终运维人员列表:", this.ywryOptions);
        } else {
          this.ywryOptions = [];
          console.error(
            "获取运维人员列表失败:",
            response.msg || response.message
          );
        }
      } catch (error) {
        console.error("获取运维人员列表异常:", error);
        this.ywryOptions = [];
        this.$message.error("获取运维人员列表失败");
      } finally {
        this.ywryLoading = false;
      }
    },

    // 搜索用户列表
    searchUserList(query) {
      this.userLoading = true;
      this.getUserList(query);
    },

    // 获取用户列表
    async getUserList(keyword = "") {
      try {
        const params = {
          pageNum: 1,
          pageSize: 100,
          status: "0", // 正常状态
        };

        // 如果有关键词，添加用户名搜索
        if (keyword && keyword.trim()) {
          params.userName = keyword.trim();
        }

        console.log("搜索用户参数:", params);
        const response = await listUser(params);
        console.log("用户搜索响应:", response);

        if (response.code === 200 && response.rows) {
          this.userOptions = response.rows;
          console.log("用户列表:", this.userOptions);
        } else {
          this.userOptions = [];
          console.error("获取用户列表失败:", response.msg || response.message);
        }
      } catch (error) {
        console.error("获取用户列表异常:", error);
        this.userOptions = [];
        this.$message.error("获取用户列表失败");
      } finally {
        this.userLoading = false;
      }
    },

    // 获取部门树结构
    async getDeptTree() {
      try {
        console.log("开始获取部门树结构...");
        const response = await deptTreeSelect();

        console.log("部门树响应:", response);

        if (response.code === 200 && response.data) {
          this.deptOptions = response.data;
          this.enabledDeptOptions = this.filterDisabledDept(
            JSON.parse(JSON.stringify(response.data))
          );
          console.log("部门树加载完成:", this.enabledDeptOptions);
        } else {
          this.deptOptions = [];
          this.enabledDeptOptions = [];
          console.error("获取部门树失败:", response.msg || response.message);
        }
      } catch (error) {
        console.error("获取部门树异常:", error);
        this.deptOptions = [];
        this.enabledDeptOptions = [];
        this.$message.error("获取部门树失败");
      }
    },

    // 过滤禁用的部门
    filterDisabledDept(deptList) {
      return deptList.filter((dept) => {
        if (dept.disabled) {
          return false;
        }
        if (dept.children && dept.children.length) {
          dept.children = this.filterDisabledDept(dept.children);
        }
        return true;
      });
    },

    // 加载开发厂商联系人
    async loadDeveloperContacts(supplierId) {
      try {
        console.log("Loading developer contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.developerContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log(
            "Developer contacts loaded:",
            this.developerContactOptions
          );

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csDeveloperUserId) {
            const selectedContact = this.developerContactOptions.find(
              (contact) => contact.id === this.myForm.csDeveloperUserId
            );
            if (selectedContact) {
              this.myForm.developerContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充开发厂商联系电话:",
                this.myForm.developerContactPhone
              );
            }
          }
        } else {
          this.developerContactOptions = [];
          console.warn("Failed to load developer contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading developer contacts:", error);
        this.developerContactOptions = [];
      }
    },

    // 加载运维厂商联系人
    async loadOperatorContacts(supplierId) {
      try {
        console.log("Loading operator contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.operatorContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log("Operator contacts loaded:", this.operatorContactOptions);

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csOperatorContactId) {
            const selectedContact = this.operatorContactOptions.find(
              (contact) => contact.id === this.myForm.csOperatorContactId
            );
            if (selectedContact) {
              this.myForm.operatorContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充运维厂商联系电话:",
                this.myForm.operatorContactPhone
              );
            }
          }
        } else {
          this.operatorContactOptions = [];
          console.warn("Failed to load operator contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading operator contacts:", error);
        this.operatorContactOptions = [];
      }
    },

    // 加载安全厂商联系人
    async loadSecurityContacts(supplierId) {
      try {
        console.log("Loading security contacts for supplier:", supplierId);
        const response = await listContactsBySupplier(supplierId);
        if (response.code === 200 && response.data) {
          // listNotPage接口直接返回数组数据
          const contactList = Array.isArray(response.data) ? response.data : [];
          this.securityContactOptions = contactList.map((contact) => ({
            id: String(contact.id), // 确保ID为字符串类型
            name: contact.userName || contact.nickName || "未知联系人",
            phone: contact.phone || "",
          }));
          console.log("Security contacts loaded:", this.securityContactOptions);

          // 如果当前有选中的联系人，自动填充联系电话
          if (this.myForm.csSecurityVendorContactId) {
            const selectedContact = this.securityContactOptions.find(
              (contact) => contact.id === this.myForm.csSecurityVendorContactId
            );
            if (selectedContact) {
              this.myForm.securityContactPhone = selectedContact.phone || "";
              console.log(
                "自动填充安全厂商联系电话:",
                this.myForm.securityContactPhone
              );
            }
          }
        } else {
          this.securityContactOptions = [];
          console.warn("Failed to load security contacts:", response.msg);
        }
      } catch (error) {
        console.error("Error loading security contacts:", error);
        this.securityContactOptions = [];
      }
    },

    // 预加载厂商选项以确保正确回显
    async preloadVendorOptions() {
      try {
        console.log("开始预加载厂商选项...");
        console.log("当前表单厂商ID:", {
          csDeveloperId: this.myForm.csDeveloperId,
          csOperatorId: this.myForm.csOperatorId,
          csSecurityVendorId: this.myForm.csSecurityVendorId,
        });

        // 收集需要预加载的厂商ID
        const vendorIds = [];
        if (this.myForm.csDeveloperId)
          vendorIds.push(this.myForm.csDeveloperId);
        if (this.myForm.csOperatorId) vendorIds.push(this.myForm.csOperatorId);
        if (this.myForm.csSecurityVendorId)
          vendorIds.push(this.myForm.csSecurityVendorId);

        console.log("需要预加载的厂商ID:", vendorIds);

        if (vendorIds.length === 0) {
          console.log("没有需要预加载的厂商ID，跳过预加载");
          return;
        }

        // 查询厂商信息
        console.log("调用厂商列表API...");
        const response = await listSupplier({
          pageNum: 1,
          pageSize: 1000,
        });

        console.log("厂商列表API响应:", response);

        if (response.code === 200 && response.data && response.data.list) {
          const allVendors = response.data.list;
          console.log("获取到的所有厂商:", allVendors);

          // 为每个厂商类型设置对应的选项
          if (this.myForm.csDeveloperId) {
            const developerVendor = allVendors.find(
              (v) => v.id == this.myForm.csDeveloperId
            ); // 使用 == 而不是 === 以处理类型转换
            console.log(
              "查找开发厂商:",
              this.myForm.csDeveloperId,
              "找到:",
              developerVendor
            );
            if (developerVendor) {
              this.developerVendorOptions = [
                {
                  id: String(developerVendor.id), // 确保ID为字符串类型
                  name: developerVendor.gysName,
                  code: developerVendor.tyxydm, // 统一社会信用代码
                },
              ];
              // 自动填充统一社会信用代码
              this.myForm.developerVendorCode = developerVendor.tyxydm || "";
              console.log("设置开发厂商选项:", this.developerVendorOptions);
              console.log(
                "自动填充开发厂商统一社会信用代码:",
                this.myForm.developerVendorCode
              );
            }
          }

          if (this.myForm.csOperatorId) {
            const operatorVendor = allVendors.find(
              (v) => v.id == this.myForm.csOperatorId
            ); // 使用 == 而不是 === 以处理类型转换
            console.log(
              "查找运维厂商:",
              this.myForm.csOperatorId,
              "找到:",
              operatorVendor
            );
            if (operatorVendor) {
              this.operatorVendorOptions = [
                {
                  id: String(operatorVendor.id), // 确保ID为字符串类型
                  name: operatorVendor.gysName,
                  code: operatorVendor.tyxydm, // 统一社会信用代码
                },
              ];
              // 自动填充统一社会信用代码
              this.myForm.operatorVendorCode = operatorVendor.tyxydm || "";
              console.log("设置运维厂商选项:", this.operatorVendorOptions);
              console.log(
                "自动填充运维厂商统一社会信用代码:",
                this.myForm.operatorVendorCode
              );
            }
          }

          if (this.myForm.csSecurityVendorId) {
            const securityVendor = allVendors.find(
              (v) => v.id == this.myForm.csSecurityVendorId
            ); // 使用 == 而不是 === 以处理类型转换
            console.log(
              "查找安全厂商:",
              this.myForm.csSecurityVendorId,
              "找到:",
              securityVendor
            );
            if (securityVendor) {
              this.securityVendorOptions = [
                {
                  id: String(securityVendor.id), // 确保ID为字符串类型
                  name: securityVendor.gysName,
                  code: securityVendor.tyxydm, // 统一社会信用代码
                },
              ];
              // 自动填充统一社会信用代码
              this.myForm.securityVendorCode = securityVendor.tyxydm || "";
              console.log("设置安全厂商选项:", this.securityVendorOptions);
              console.log(
                "自动填充安全厂商统一社会信用代码:",
                this.myForm.securityVendorCode
              );
            }
          }

          console.log("预加载完成，最终厂商选项:", {
            developer: this.developerVendorOptions,
            operator: this.operatorVendorOptions,
            security: this.securityVendorOptions,
          });

          // 强制触发Vue响应式更新
          this.$forceUpdate();
          console.log("强制更新视图完成");
        } else {
          console.error("厂商列表API响应异常:", response);
        }
      } catch (error) {
        console.error("预加载厂商选项异常:", error);
      }
    },

    // 格式化日期为 YYYY-MM-DD 格式
    formatDate(date) {
      if (!date) return null;
      if (typeof date === "string") return date;
      if (date instanceof Date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        return `${year}-${month}-${day}`;
      }
      return null;
    },

    // 解析多选值 - 将后端返回的字符串或数字转换为数组
    parseMultiSelectValue(value) {
      if (!value && value !== 0) return [];
      if (Array.isArray(value)) return value;
      if (typeof value === 'string') {
        // 如果是逗号分隔的字符串，分割后转换为数字数组
        if (value.includes(',')) {
          return value.split(',').map(item => parseInt(item.trim())).filter(item => !isNaN(item));
        }
        // 如果是单个字符串数字，转换为数字数组
        const num = parseInt(value);
        return isNaN(num) ? [] : [num];
      }
      if (typeof value === 'number') {
        return [value];
      }
      return [];
    },

    // 格式化多选值 - 将数组转换为后端需要的格式
    formatMultiSelectValue(value) {
      if (!value || !Array.isArray(value) || value.length === 0) return null;
      // 如果只有一个值，直接返回该值
      if (value.length === 1) {
        return parseInt(value[0]);
      }
      // 如果有多个值，返回逗号分隔的字符串
      return value.map(item => parseInt(item)).join(',');
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding-bottom: 120px; // 增加底部内边距，为固定的操作栏留出空间
  background-color: #f5f5f5;
  min-height: 100vh;
  overflow-y: auto; // 确保可以滚动
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 15px;
  margin-bottom: 12px;

  .back-btn {
    font-size: 16px;
    color: #0057fe;
    margin-right: 12px;

    &:hover {
      color: #003db8;
    }
  }

  .page-title {
    margin: 0;
    font-family: Source Han Sans, Source Han Sans;
    font-weight: 700;
    font-size: 20px;
    color: #1d2129;
    line-height: 28px;
  }

  .header-actions {
    display: flex;
    gap: 12px;
  }
}

.edit-form {
  .card {
    border-radius: 15px;
    padding: 20px 20px;
    box-sizing: border-box;
    background-color: #fff;
    height: auto;
    margin-bottom: 12px;
    overflow: visible; // 确保卡片内容可见

    .cardTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 18px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin-bottom: 10px;
      padding-left: 18px;
      box-sizing: border-box;
      position: relative;

      &::before {
        content: "";
        position: absolute;
        top: 2px;
        left: 0;
        width: 10px;
        height: 20px;
        background: url("~@/assets/images/cardTitle_icon.png");
        background-size: 100% 100%;
      }
    }

    .lineTitle {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 24px;
      text-align: left;
      margin: 12px 0 0 32px;
    }
  }
}

.itemList {
  flex-wrap: wrap;
  padding: 0 20px;
  box-sizing: border-box;

  .item_grid_3 {
    width: 30%;
    margin-left: 16px;
    margin-bottom: 20px;
  }
}

.footer-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #e5e6eb;
  padding: 20px;
  display: flex;
  justify-content: center;
  gap: 12px;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); // 添加阴影效果
}

.tableEmpty {
  padding: 10px 0 16px 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: Source Han Sans CN, Source Han Sans CN;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  line-height: 22px;
  text-align: center;

  .img {
    width: 82px;
    height: 82px;
    margin-bottom: 6px;
  }
}

// 全局样式覆盖
::v-deep .el-form-item__label {
  padding: 0;
  line-height: 30px;
}

::v-deep .el-form-item {
  margin-bottom: 10px;
}

::v-deep .el-table {
  border-radius: 8px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #f8f9fa;
}

// 确保页面可以正常滚动
body {
  overflow-y: auto !important;
}

// 表格容器样式
::v-deep .el-table__body-wrapper {
  overflow-x: auto;
}

// 确保表格内的输入框和选择器正常显示
::v-deep .el-table .el-input,
::v-deep .el-table .el-select {
  width: 100%;
}

// 空状态样式
.empty {
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #999;

  .img {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
  }
}
</style>
